<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.bassims</groupId>
    <artifactId>atouradmin</artifactId>
    <packaging>pom</packaging>
    <version>2.6</version>

    <modules>
        <module>atouradmin-common</module>
        <module>atouradmin-logging</module>
        <module>atouradmin-system</module>
        <module>atouradmin-tools</module>
        <module>atouradmin-generator</module>
    </modules>

    <name>ATOUR-ADMIN 后台管理</name>
    <url>http://auauz.net</url>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.1.0.RELEASE</version>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <log4jdbc.version>1.16</log4jdbc.version>
        <swagger.version>2.9.2</swagger.version>
        <fastjson.version>1.2.83</fastjson.version>
        <druid.version>1.1.22</druid.version>
        <commons-pool2.version>2.5.0</commons-pool2.version>
        <mapstruct.version>1.3.1.Final</mapstruct.version>
        <spring-cloud.version>Greenwich.SR6</spring-cloud.version>
        <apollo.version>1.9.2</apollo.version>
    </properties>

    <dependencies>
        <!--Spring boot 核心-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <!--Spring boot Web容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--Spring boot 测试-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!--Spring boot 安全框架-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>

        <!-- spring boot 缓存 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>

        <!--Spring boot Redis-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <!--spring boot 集成redis所需common-pool2-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>${commons-pool2.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <!--监控sql日志-->
        <dependency>
            <groupId>org.bgee.log4jdbc-log4j2</groupId>
            <artifactId>log4jdbc-log4j2-jdbc4.1</artifactId>
            <version>${log4jdbc.version}</version>
        </dependency>

        <!-- Swagger UI 相关 -->
        <!--        <dependency>-->
        <!--            <groupId>io.springfox</groupId>-->
        <!--            <artifactId>springfox-swagger2</artifactId>-->
        <!--            <version>${swagger.version}</version>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <groupId>io.swagger</groupId>-->
        <!--                    <artifactId>swagger-annotations</artifactId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>io.swagger</groupId>-->
        <!--                    <artifactId>swagger-models</artifactId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>io.springfox</groupId>-->
        <!--            <artifactId>springfox-swagger-ui</artifactId>-->
        <!--            <version>${swagger.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>io.swagger</groupId>-->
        <!--            <artifactId>swagger-annotations</artifactId>-->
        <!--            <version>1.5.21</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>io.swagger</groupId>-->
        <!--            <artifactId>swagger-models</artifactId>-->
        <!--            <version>1.5.21</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>2.0.4</version>
        </dependency>

        <!--Mysql依赖包-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- druid数据源驱动 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid.version}</version>
        </dependency>
        <dependency>
            <groupId>org.lionsoul</groupId>
            <artifactId>ip2region</artifactId>
            <version>1.7.2</version>
        </dependency>

        <!--lombok插件-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- excel工具 -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>xerces</groupId>
            <artifactId>xercesImpl</artifactId>
            <version>2.12.2</version>
        </dependency>
<!--        excel export-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.1</version>
        </dependency>

        <!-- fastjson -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>

        <!--mapStruct依赖-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.inject</groupId>
            <artifactId>javax.inject</artifactId>
            <version>1</version>
        </dependency>

        <!-- Java图形验证码 -->
        <dependency>
            <groupId>com.github.whvcse</groupId>
            <artifactId>easy-captcha</artifactId>
            <version>1.6.2</version>
        </dependency>

        <!-- 解析客户端操作系统、浏览器信息 -->
        <dependency>
            <groupId>eu.bitwalker</groupId>
            <artifactId>UserAgentUtils</artifactId>
            <version>1.21</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.baomidou</groupId>-->
        <!--            <artifactId>mybatis-plus-boot-starter</artifactId>-->
        <!--            <version>3.4.1</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.4.3.2</version>
        </dependency>
        <dependency>
            <groupId>com.drewnoakes</groupId>
            <artifactId>metadata-extractor</artifactId>
            <version>2.16.0</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>3.5.1</version>
        </dependency>

        <!--redis分布式锁-->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.14.0</version>
        </dependency>

        <!--图片压缩工具类thumbnailator-->
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.8</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.apache.poi</groupId>-->
<!--            <artifactId>poi</artifactId>-->
<!--            <version>5.0.0</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>

        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.3.1</version>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>5.1.10</version>
        </dependency>
        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
            <version>3.1</version>
        </dependency>
        <!--freemarker-->
<!--        <dependency>-->
<!--            <groupId>org.freemarker</groupId>-->
<!--            <artifactId>freemarker</artifactId>-->
<!--            <version>2.3.26-incubating</version>-->
<!--        </dependency>-->
        <!--jfreechart-->



        <!-- https://mvnrepository.com/artifact/com.github.pagehelper/pagehelper-spring-boot-starter -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>1.2.5</version>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis-spring</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Spring Cloud Eureka Client -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>

        <!-- Apollo 配置中心客户端 -->
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>${apollo.version}</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- 统一管理 BouncyCastle 版本，避免循环依赖问题 -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>1.68</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk15on</artifactId>
                <version>1.68</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <!-- 本地开发环境 -->
            <id>local</id>
            <properties>
                <profile.active>local</profile.active>
            </properties>
        </profile>

        <profile>
            <!-- 开发环境 -->
            <id>dev</id>
            <properties>
                <profile.active>dev</profile.active>
            </properties>
        </profile>

        <profile>
            <!-- 本地开发环境 -->
            <id>local_dev</id>
            <properties>
                <profile.active>dev</profile.active>
            </properties>
        </profile>

        <profile>
            <!-- QA -->
            <id>qa</id>
            <properties>
                <profile.active>qa</profile.active>
            </properties>
        </profile>

        <profile>
            <!-- 测试环境 -->
            <id>test</id>
            <properties>
                <profile.active>test</profile.active>
            </properties>
        </profile>

        <profile>
            <!-- 测试环境 -->
            <id>beta</id>
            <properties>
                <profile.active>test</profile.active>
            </properties>
        </profile>

        <profile>
            <!-- 发布环境 -->
            <id>prod</id>
            <properties>
                <profile.active>prod</profile.active>
            </properties>
        </profile>
        <profile>
            <!-- 发布环境 -->
            <id>pre</id>
            <properties>
                <profile.active>pre</profile.active>
            </properties>
        </profile>
    </profiles>

    <build>
        <plugins>
            <!-- 打包时跳过测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>com.google.cloud.tools</groupId>-->
<!--                <artifactId>jib-maven-plugin</artifactId>-->
<!--                <version>2.7.0</version>-->
<!--                <configuration>-->
<!--                    &lt;!&ndash;配置基本镜像&ndash;&gt;-->
<!--                    <from>-->
<!--                        <image>registry.cn-hangzhou.aliyuncs.com/xiaoqianh/openjdk-zh</image>-->
<!--                    </from>-->
<!--                    &lt;!&ndash;配置最终推送的地址，仓库名，镜像名&ndash;&gt;-->
<!--                    <to>-->
<!--                        <image>registry.cn-hangzhou.aliyuncs.com/xiaoqianh/shijin</image>-->
<!--                        <auth>-->
<!--                            &lt;!&ndash;账号密码,`Docker Hub`账号密码为登录账号密码,阿里云镜像仓库账号为登录账号,密码可去镜像仓库修改固定密码或获取临时密码&ndash;&gt;-->
<!--                            <username>小钱xiaoqianh</username>-->
<!--                            <password>Aa456138</password>-->
<!--                        </auth>-->
<!--                    </to>-->
<!--                    <container>-->
<!--                        &lt;!&ndash;jvm内存参数&ndash;&gt;-->
<!--                        <jvmFlags>-->
<!--                            <jvmFlag>-Xms4g</jvmFlag>-->
<!--                            <jvmFlag>-Xmx4g</jvmFlag>-->
<!--                        </jvmFlags>-->
<!--                        &lt;!&ndash;要暴露的端口&ndash;&gt;-->
<!--                        <ports>-->
<!--                            <port>8000</port>-->
<!--                        </ports>-->
<!--                        <mainClass>-->
<!--                            AppRun-->
<!--                        </mainClass>-->
<!--                        &lt;!&ndash; 设置时区  &ndash;&gt;-->
<!--                        <environment>-->
<!--                            <TZ>Asia/Shanghai</TZ>-->
<!--                        </environment>-->
<!--                        <creationTime>USE_CURRENT_TIMESTAMP</creationTime>-->
<!--                    </container>-->
<!--                </configuration>-->
<!--                &lt;!&ndash;绑定到maven lifecicle&ndash;&gt;-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <phase>package</phase>-->
<!--                        <goals>-->
<!--                            <goal>build</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
            <repository>
                <id>com.e-iceblue</id>
                <url>http://repo.e-iceblue.cn/repository/maven-public/</url>
            </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
</project>
